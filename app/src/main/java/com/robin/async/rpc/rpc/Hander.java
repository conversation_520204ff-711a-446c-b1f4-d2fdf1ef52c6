package com.robin.async.rpc.rpc;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

public class Hander implements IXposedHookLoadPackage {

    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        try {
            // Hook小程序网络请求入口
            XposedHelpers.findAndHookMethod(
                    "com.tencent.mm.appbrand.commonjni.AppBrandCommonBindingJni",
                    lpparam.classLoader,
                    "nativeInvokeHandler",
                    String.class, String.class, String.class,
                    Integer.TYPE, Boolean.TYPE, Integer.TYPE, Integer.TYPE,
                    new XC_MethodHook() {
                        @Override
                        protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                            String requestData = param.args[1].toString();

                            // 检查是否为目标小程序的库存查询请求
                            if (isStockQueryRequest(requestData)) {
                                // 保存JNI对象用于后续调用
                                jniObject = param.thisObject;
                                // 解析请求参数
                                parseStockRequest(requestData);
                            }
                        }
                    }
            );
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook网络请求失败: " + e.getMessage());
        }
    }
}
