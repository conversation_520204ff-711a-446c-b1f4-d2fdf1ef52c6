[versions]
agp = "8.10.1"
hiltCompilerVersion = "2.48"
kotlin = "2.0.21"
junit = "4.13.2"
appcompat = "1.7.1"
activityComposeVersion = "1.10.1"
lifecycleRuntimeKtxVersion = "2.9.1"
androidxJunit = "1.2.1"
hiltNavigationCompose = "1.2.0"
coreKtxVersion = "1.12.0"
navigationCompose = "2.9.0"
retrofitVersion = "2.9.0"
rhinoAndroidVersion = "1.3.0"
rhinoVersion = "1.8.0"
roomRuntimeVersion = "2.6.0"
hilt = "2.48"
androidbrowserhelperVersion = "2.6.1"
material = "1.12.0"
material3 = "1.3.2"
composeBom = "2024.09.00"
hiltAndroid = "2.49"
hutoolAll = "5.8.30"
uiVersion = "1.6.0"
xposed = "82"
sekiroBusinessApi = "1.4"

[libraries]
androidx-activity-compose-v182 = { module = "androidx.activity:activity-compose", version.ref = "activityComposeVersion" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-core-ktx-v1120 = { module = "androidx.core:core-ktx", version.ref = "coreKtxVersion" }
androidx-hilt-navigation-compose = { module = "androidx.hilt:hilt-navigation-compose", version.ref = "hiltNavigationCompose" }
androidx-junit-v121 = { module = "androidx.test.ext:junit", version.ref = "androidxJunit" }
androidx-lifecycle-runtime-ktx-v270 = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtxVersion" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigationCompose" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "roomRuntimeVersion" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "roomRuntimeVersion" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "roomRuntimeVersion" }
androidbrowserhelper = { group = "com.google.androidbrowserhelper", name = "androidbrowserhelper", version.ref = "androidbrowserhelperVersion" }
androidx-ui = { module = "androidx.compose.ui:ui", version.ref = "uiVersion" }
androidx-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4", version.ref = "uiVersion" }
androidx-ui-tooling = { module = "androidx.compose.ui:ui-tooling", version.ref = "uiVersion" }
androidx-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview", version.ref = "uiVersion" }
converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofitVersion" }
hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "hiltCompilerVersion" }
material = { module = "com.google.android.material:material", version.ref = "material" }
material3 = { module = "androidx.compose.material3:material3", version.ref = "material3" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hiltAndroid" }
hutool-all = { module = "cn.hutool:hutool-all", version.ref = "hutoolAll" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofitVersion" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
rhino = { module = "org.mozilla:rhino", version.ref = "rhinoVersion" }
rhino-android = { module = "io.apisense:rhino-android", version.ref = "rhinoAndroidVersion" }
xposed-v82 = { module = "de.robv.android.xposed:api", version.ref = "xposed" }
sekiro-business-api = { module = "com.virjar.sekiro.business:sekiro-business-api", version.ref = "sekiroBusinessApi" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }

compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

